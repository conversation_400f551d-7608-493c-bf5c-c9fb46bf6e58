import { test, expect } from '@playwright/test';
import { HomePage } from './pages/HomePage';
import { SearchResultsPage } from './pages/SearchResultsPage';
import { ListingDetailsPage } from './pages/ListingDetailsPage';
import { ListingPaymentPage } from './pages/ListingPaymentPage';

test('Airbnb Booking Flow with Page Objects', async ({ page }) => {
    // Initialize page objects
    const homePage = new HomePage(page);
    const searchResultsPage = new SearchResultsPage(page);
    
    // Navigate to home page and perform search
    await homePage.navigateToHomePage();
    await homePage.waitForHomePageLoad();
    await homePage.searchForListing('hong kong', 2, 1);
    
    // Handle search results and select listing
    await searchResultsPage.waitForSearchResults();
    const listingPage = await searchResultsPage.clickFirstListing();
    
    // Initialize page objects for the new popup page
    const listingDetailsPage = new ListingDetailsPage(listingPage);
    const listingPaymentPage = new ListingPaymentPage(listingPage);
    
    // Complete booking flow on listing details page
    await listingDetailsPage.completeBookingFlow();
    
    // Complete payment/booking process
    await listingPaymentPage.completeBooking();
});

test('Airbnb Search Flow - Individual Steps', async ({ page }) => {
    // Initialize page objects
    const homePage = new HomePage(page);
    const searchResultsPage = new SearchResultsPage(page);
    
    // Step 1: Navigate to home page
    await homePage.navigateToHomePage();
    await homePage.waitForHomePageLoad();
    
    // Step 2: Search for destination
    await homePage.searchForDestination('hong kong');
    
    // Step 3: Select dates
    await homePage.selectCheckInDate();
    await homePage.selectCheckOutDate();
    
    // Step 4: Add guests
    await homePage.addGuests(2, 1);
    
    // Step 5: Perform search
    await homePage.performSearch();
    
    // Step 6: Verify search results
    await searchResultsPage.waitForSearchResults();
    const listingCount = await searchResultsPage.getListingCount();
    expect(listingCount).toBeGreaterThan(0);
});
