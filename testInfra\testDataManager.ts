import { StayDates } from './interfaces/stayDates';

export class TestDataManager {
  static getValidDates({
    amountOfDaysFromToday = 1,
    lengthOfStayInDays = 1,
  }: {
    amountOfDaysFromToday: number;
    lengthOfStayInDays: number;
  }): StayDates {
    const checkInDate = new Date();
    checkInDate.setDate(checkInDate.getDate() + amountOfDaysFromToday);

    const checkOutDate = new Date(checkInDate);
    checkOutDate.setDate(checkOutDate.getDate() + lengthOfStayInDays);
return { checkInDate, checkOutDate };
  }

  static convertDatesToLocatorFormat({ checkInDate, checkOutDate }: StayDates): {
    checkInLocator: string;
    checkOutLocator: string;
  } {
    const checkInLocator = checkInDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
    const checkOutLocator = checkOutDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });

    return { checkInLocator, checkOutLocator };
  }
}
