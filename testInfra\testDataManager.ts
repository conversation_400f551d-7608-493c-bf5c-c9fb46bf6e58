import { StayDates } from "./interfaces/stayDates";

export class TestDataManager {
static getValidDates({ amountOfDaysFromToday = 1, lengthOfStay = 1 }: { amountOfDaysFromToday: number, lengthOfStay: number }): StayDates {
    const checkInDate = new Date();
    checkInDate.setDate(checkInDate.getDate() + amountOfDaysFromToday);

    const checkOutDate = new Date(checkInDate);
    checkOutDate.setDate(checkOutDate.getDate() + lengthOfStay);

    return { checkInDate, checkOutDate };
}

}