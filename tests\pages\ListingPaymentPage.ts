import { Page, Locator } from '@playwright/test';

export class ListingPaymentPage {
    private page: Page;

    // Locators (sorted alphabetically)
    private readonly bookingButton: Locator;
    private readonly confirmBookingButton: Locator;
    private readonly paymentForm: Locator;
    private readonly priceBreakdown: Locator;
    private readonly reserveButton: Locator;

    constructor(page: Page) {
        this.page = page;
        
        // Initialize locators (sorted alphabetically)
        this.bookingButton = page.getByRole('button', { name: 'הזמנה' });
        this.confirmBookingButton = page.getByRole('button', { name: 'אישור הזמנה' });
        this.paymentForm = page.locator('[data-testid="payment-form"]');
        this.priceBreakdown = page.locator('[data-testid="price-breakdown"]');
        this.reserveButton = page.getByRole('button', { name: 'Reserve' });
    }

    async waitForPaymentPage(): Promise<void> {
        await this.bookingButton.waitFor({ state: 'visible' });
    }

    async confirmBooking(): Promise<void> {
        await this.bookingButton.click();
    }

    async finalizeBooking(): Promise<void> {
        // This method handles the final booking confirmation
        // In the original test, the booking button is clicked twice
        await this.bookingButton.click();
    }

    async fillPaymentDetails(cardNumber: string, expiryDate: string, cvv: string): Promise<void> {
        // These locators would need to be updated based on actual payment form structure
        // This is a placeholder implementation
        const cardNumberInput = this.page.locator('[data-testid="card-number"]');
        const expiryInput = this.page.locator('[data-testid="expiry-date"]');
        const cvvInput = this.page.locator('[data-testid="cvv"]');

        await cardNumberInput.fill(cardNumber);
        await expiryInput.fill(expiryDate);
        await cvvInput.fill(cvv);
    }

    async getPriceBreakdown(): Promise<string> {
        return await this.priceBreakdown.textContent() || '';
    }

    async isPaymentFormVisible(): Promise<boolean> {
        return await this.paymentForm.isVisible();
    }

    async completeBooking(): Promise<void> {
        await this.confirmBooking();
        await this.finalizeBooking();
    }
}
