import { test, expect } from '@playwright/test';

test('Arbnb', async ({ page }) => {
  //TODO: Set the location to english for better locators
  await page.goto('https://he.airbnb.com/?_set_bev_on_new_domain=1750856737_EAZTJhYzM0Yjg4ZG');
  //TODO: wait after load by using the listing cards
  await expect(page.getByTestId('structured-search-input-field-query')).toBeVisible();
  await page.getByTestId('structured-search-input-field-query').click();
  await page.getByTestId('structured-search-input-field-query').fill('hong kong');
  await page.getByRole('option', { name: 'Hong Kong' }).first().click();
  await page.getByRole('button', { name: '25, Wednesday, June 2025' }).click();
  await page.getByRole('button', { name: '26, Thursday, June 2025' }).click();
  await page.getByRole('button', { name: 'מי הוספת אורחים' }).click();
  await page.getByTestId('stepper-adults-increase-button').first().click();
  await page.getByTestId('stepper-adults-increase-button').first().click();
  await page.getByTestId('stepper-children-increase-button').first().click();
  await page.getByTestId('structured-search-input-search-button').click();

  // TODO get a list of all listings and find the highest ranking one and click it
  // Fix all locators after switching to english
  await page.getByRole('link', { name: 'דירה | מונג קוק' }).first().click();
  const page1Promise = page.waitForEvent('popup');
  await page.getByRole('link', { name: 'דירה | מונג קוק' }).first().click();
  const page1 = await page1Promise;
  await page1.getByRole('button', { name: 'סגירה' }).click();
  await page1.getByRole('button', { name: "שינוי התאריכים; צ'ק-אין: 2025" }).click();
  await page1
    .getByTestId('bookit-sidebar-availability-calendar')
    .getByRole('heading', { name: 'לילה' })
    .click();
  await page1.locator('._cuv1re').click();
  await page1.getByRole('button', { name: '3 אורחים' }).click();
  await page1.getByTestId('GuestPicker-book_it-form-children-stepper-decrease-button').click();
  await page1.getByRole('button', { name: "שינוי התאריכים; צ'ק-אין: 2025" }).click();
  await page1
    .getByTestId('bookit-sidebar-availability-calendar')
    .locator('td')
    .filter({ hasText: /^9$/ })
    .nth(2)
    .click();
  await page1.getByRole('textbox', { name: "צ'ק-אאוט" }).click();
  await page1
    .getByRole('button', {
      name: "NaN, Invalid date, Invalid date NaN. זמין. יש לבחור בתור תאריך צ'ק-אאוט",
    })
    .nth(2)
    .click();
  await page1.getByRole('button', { name: 'הזמנה' }).click();
  await page1.getByRole('button', { name: 'הזמנה' }).click();
});
