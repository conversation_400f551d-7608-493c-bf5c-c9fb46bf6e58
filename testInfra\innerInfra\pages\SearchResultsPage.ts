import { Page, Locator } from '@playwright/test';

export class SearchResultsPage {
  private page: Page;

  // Locators (sorted alphabetically)
  readonly listingLinks: Locator;
  readonly mongKokApartmentLink: Locator;

  constructor(page: Page) {
    this.page = page;

    // Initialize locators (sorted alphabetically)
    this.listingLinks = page.getByRole('link').filter({ hasText: 'דירה' });
    this.mongKokApartmentLink = page.getByRole('link', { name: 'דירה | מונג קוק' }).first();
  }
}
