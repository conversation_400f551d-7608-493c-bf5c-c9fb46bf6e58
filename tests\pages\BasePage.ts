import { Page, Locator } from '@playwright/test';

export abstract class BasePage {
    protected page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Wait for a specific locator to be visible
     */
    protected async waitForElement(locator: Locator, timeout: number = 30000): Promise<void> {
        await locator.waitFor({ state: 'visible', timeout });
    }

    /**
     * Wait for a specific locator to be hidden
     */
    protected async waitForElementToHide(locator: Locator, timeout: number = 30000): Promise<void> {
        await locator.waitFor({ state: 'hidden', timeout });
    }

    /**
     * Click an element with optional timeout
     */
    protected async clickElement(locator: Locator, timeout: number = 30000): Promise<void> {
        await this.waitForElement(locator, timeout);
        await locator.click();
    }

    /**
     * Fill text in an input field
     */
    protected async fillText(locator: Locator, text: string, timeout: number = 30000): Promise<void> {
        await this.waitForElement(locator, timeout);
        await locator.fill(text);
    }

    /**
     * Get text content from an element
     */
    protected async getTextContent(locator: Locator, timeout: number = 30000): Promise<string> {
        await this.waitForElement(locator, timeout);
        return await locator.textContent() || '';
    }

    /**
     * Check if an element is visible
     */
    protected async isElementVisible(locator: Locator): Promise<boolean> {
        try {
            return await locator.isVisible();
        } catch {
            return false;
        }
    }

    /**
     * Get the current page URL
     */
    async getCurrentUrl(): Promise<string> {
        return this.page.url();
    }

    /**
     * Get the page title
     */
    async getPageTitle(): Promise<string> {
        return await this.page.title();
    }

    /**
     * Wait for page to load completely
     */
    async waitForPageLoad(): Promise<void> {
        await this.page.waitForLoadState('networkidle');
    }

    /**
     * Take a screenshot
     */
    async takeScreenshot(name: string): Promise<void> {
        await this.page.screenshot({ path: `screenshots/${name}.png` });
    }
}
