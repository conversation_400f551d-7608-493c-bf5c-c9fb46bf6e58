import { Page, Locator } from '@playwright/test';

export class ListingPaymentPage {
  private page: Page;

  // Locators (sorted alphabetically)
  readonly bookingButton: Locator;
  readonly cardNumberInput: Locator;
  readonly confirmBookingButton: Locator;
  readonly cvvInput: Locator;
  readonly expiryInput: Locator;
  readonly paymentForm: Locator;
  readonly priceBreakdown: Locator;
  readonly reserveButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // Initialize locators (sorted alphabetically)
    this.bookingButton = page.getByRole('button', { name: 'הזמנה' });
    this.cardNumberInput = page.locator('[data-testid="card-number"]');
    this.confirmBookingButton = page.getByRole('button', { name: 'אישור הזמנה' });
    this.cvvInput = page.locator('[data-testid="cvv"]');
    this.expiryInput = page.locator('[data-testid="expiry-date"]');
    this.paymentForm = page.locator('[data-testid="payment-form"]');
    this.priceBreakdown = page.locator('[data-testid="price-breakdown"]');
    this.reserveButton = page.getByRole('button', { name: 'Reserve' });
  }
}
