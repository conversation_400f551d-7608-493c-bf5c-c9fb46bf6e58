import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';

export class HomePage extends BasePage {

    // Locators (sorted alphabetically)
    private readonly addGuestsButton: Locator;
    private readonly checkInDateButton: Locator;
    private readonly checkOutDateButton: Locator;
    private readonly destinationInput: Locator;
    private readonly hongKongOption: Locator;
    private readonly searchButton: Locator;
    private readonly stepperAdultsIncreaseButton: Locator;
    private readonly stepperChildrenIncreaseButton: Locator;

    constructor(page: Page) {
        super(page);
        
        // Initialize locators (sorted alphabetically)
        this.addGuestsButton = page.getByRole('button', { name: 'מי הוספת אורחים' });
        this.checkInDateButton = page.getByRole('button', { name: '25, Wednesday, June 2025' });
        this.checkOutDateButton = page.getByRole('button', { name: '26, Thursday, June 2025' });
        this.destinationInput = page.getByTestId('structured-search-input-field-query');
        this.hongKongOption = page.getByRole('option', { name: 'Hong Kong' }).first();
        this.searchButton = page.getByTestId('structured-search-input-search-button');
        this.stepperAdultsIncreaseButton = page.getByTestId('stepper-adults-increase-button').first();
        this.stepperChildrenIncreaseButton = page.getByTestId('stepper-children-increase-button').first();
    }

    async navigateToHomePage(): Promise<void> {
        await this.page.goto('https://he.airbnb.com/?_set_bev_on_new_domain=1750856737_EAZTJhYzM0Yjg4ZG');
    }

    async waitForHomePageLoad(): Promise<void> {
        await this.waitForElement(this.destinationInput);
    }

    async searchForDestination(destination: string): Promise<void> {
        await this.clickElement(this.destinationInput);
        await this.fillText(this.destinationInput, destination);
        await this.clickElement(this.hongKongOption);
    }

    async selectCheckInDate(): Promise<void> {
        await this.clickElement(this.checkInDateButton);
    }

    async selectCheckOutDate(): Promise<void> {
        await this.clickElement(this.checkOutDateButton);
    }

    async addGuests(adults: number = 2, children: number = 1): Promise<void> {
        await this.clickElement(this.addGuestsButton);

        // Add adults
        for (let i = 0; i < adults; i++) {
            await this.clickElement(this.stepperAdultsIncreaseButton);
        }

        // Add children
        for (let i = 0; i < children; i++) {
            await this.clickElement(this.stepperChildrenIncreaseButton);
        }
    }

    async performSearch(): Promise<void> {
        await this.clickElement(this.searchButton);
    }

    async searchForListing(destination: string, adults: number = 2, children: number = 1): Promise<void> {
        await this.searchForDestination(destination);
        await this.selectCheckInDate();
        await this.selectCheckOutDate();
        await this.addGuests(adults, children);
        await this.performSearch();
    }
}
