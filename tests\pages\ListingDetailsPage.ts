import { Page, Locator } from '@playwright/test';

export class ListingDetailsPage {
    private page: Page;

    // Locators (sorted alphabetically)
    private readonly availabilityCalendar: Locator;
    private readonly bookingButton: Locator;
    private readonly changeDatesButton: Locator;
    private readonly checkInTextbox: Locator;
    private readonly checkOutTextbox: Locator;
    private readonly closeButton: Locator;
    private readonly guestPickerDecreaseButton: Locator;
    private readonly guestsButton: Locator;
    private readonly nightHeading: Locator;
    private readonly priceSelector: Locator;

    constructor(page: Page) {
        this.page = page;
        
        // Initialize locators (sorted alphabetically)
        this.availabilityCalendar = page.getByTestId('bookit-sidebar-availability-calendar');
        this.bookingButton = page.getByRole('button', { name: 'הזמנה' });
        this.changeDatesButton = page.getByRole('button', { name: 'שינוי התאריכים; צ\'ק-אין: 2025' });
        this.checkInTextbox = page.getByRole('textbox', { name: 'צ\'ק-אין' });
        this.checkOutTextbox = page.getByRole('textbox', { name: 'צ\'ק-אאוט' });
        this.closeButton = page.getByRole('button', { name: 'סגירה' });
        this.guestPickerDecreaseButton = page.getByTestId('GuestPicker-book_it-form-children-stepper-decrease-button');
        this.guestsButton = page.getByRole('button', { name: '3 אורחים' });
        this.nightHeading = this.availabilityCalendar.getByRole('heading', { name: 'לילה' });
        this.priceSelector = page.locator('._cuv1re');
    }

    async closeModal(): Promise<void> {
        await this.closeButton.click();
    }

    async openDatePicker(): Promise<void> {
        await this.changeDatesButton.click();
    }

    async selectNightOption(): Promise<void> {
        await this.nightHeading.click();
    }

    async selectPrice(): Promise<void> {
        await this.priceSelector.click();
    }

    async openGuestPicker(): Promise<void> {
        await this.guestsButton.click();
    }

    async decreaseChildrenCount(): Promise<void> {
        await this.guestPickerDecreaseButton.click();
    }

    async selectCheckInDate(day: string): Promise<void> {
        await this.changeDatesButton.click();
        const daySelector = this.availabilityCalendar.locator('td').filter({ hasText: new RegExp(`^${day}$`) }).nth(2);
        await daySelector.click();
    }

    async selectCheckOutDate(): Promise<void> {
        await this.checkOutTextbox.click();
        const checkOutDateButton = this.page.getByRole('button', { 
            name: 'NaN, Invalid date, Invalid date NaN. זמין. יש לבחור בתור תאריך צ\'ק-אאוט' 
        }).nth(2);
        await checkOutDateButton.click();
    }

    async proceedToBooking(): Promise<void> {
        await this.bookingButton.click();
    }

    async completeBookingFlow(): Promise<void> {
        await this.closeModal();
        await this.openDatePicker();
        await this.selectNightOption();
        await this.selectPrice();
        await this.openGuestPicker();
        await this.decreaseChildrenCount();
        await this.selectCheckInDate('9');
        await this.selectCheckOutDate();
        await this.proceedToBooking();
    }
}
