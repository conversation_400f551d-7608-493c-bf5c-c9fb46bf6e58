import { Page, Locator } from '@playwright/test';

export class SearchResultsPage {
    private page: Page;

    // Locators (sorted alphabetically)
    private readonly listingLinks: Locator;
    private readonly mongKokApartmentLink: Locator;

    constructor(page: Page) {
        this.page = page;
        
        // Initialize locators (sorted alphabetically)
        this.listingLinks = page.getByRole('link').filter({ hasText: 'דירה' });
        this.mongKokApartmentLink = page.getByRole('link', { name: 'דירה | מונג קוק' }).first();
    }

    async waitForSearchResults(): Promise<void> {
        await this.listingLinks.first().waitFor({ state: 'visible' });
    }

    async clickFirstListing(): Promise<Page> {
        // Wait for popup to be created when clicking the listing
        const pagePromise = this.page.waitForEvent('popup');
        await this.mongKokApartmentLink.click();
        return await pagePromise;
    }

    async selectSpecificListing(listingName: string): Promise<Page> {
        const listingLink = this.page.getByRole('link', { name: listingName }).first();
        const pagePromise = this.page.waitForEvent('popup');
        await listingLink.click();
        return await pagePromise;
    }

    async getAllListings(): Promise<Locator> {
        return this.listingLinks;
    }

    async getListingCount(): Promise<number> {
        return await this.listingLinks.count();
    }
}
